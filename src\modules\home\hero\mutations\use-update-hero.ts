import { IHero } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseUpdateHero(){
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IHero>, Error, IHero>({
        mutationFn: (data:IHero) => 
            fetch(`https://api.trailandtreknepal.com/home-hero/${data.id}`, {
                method: "PATCH",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data),
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: ['hero'] });
                toast.success("Hero Updated Sucessfully");
            },
            onError: () => {
                toast.error("Error Updating Hero");
            }
    })
}