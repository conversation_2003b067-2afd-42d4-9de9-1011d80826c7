"use client";
import {
  createContext,
  useContext,
  useState,
  type ReactNode,
  useEffect,
} from "react";
import { useLoginMutation } from "@/modules/auth/mutations/login-mutation";
import User from "@/types/user";
import { useRouter } from "next/navigation";

type AuthContextType = {
  user: User | null;
  login: (email: string, password: string) => void;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // useEffect(() => {
  //   const checkAuth = async () => {
  //     try {
  //       const res = await fetch("https://api.trailandtreknepal.com/auth/status", {
  //         credentials: "include",
  //       });
  //       if (!res.ok) throw new Error("Not authenticated");

  //       const data = await res.json();

  //       if (data.data === true) {
  //         setUser({
  //           id: "cmei5jsvs0008hts2eoudh1bu",
  //           email: "<EMAIL>",
  //           name: "Admin",
  //           permissions: []
  //         });
  //       } else {
  //         setUser(null);
  //       }
  //     } catch {
  //       setUser(null);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   checkAuth();
  // }, []);


  useEffect(() => {
    const loadUser = () => {
      const savedUser = localStorage.getItem("user");
      if (savedUser) {
        setUser(JSON.parse(savedUser));
      }
      setIsLoading(false);
    };
    loadUser();
  }, []);

  const handleLoginSuccess = (response: { data: User }) => {
    // setUser(response.data);
    const userData = response.data;

    localStorage.setItem("user", JSON.stringify(userData));
    setUser(userData);

    router.push("/");
  };

  const loginMutation = useLoginMutation(handleLoginSuccess);

  const login = (email: string, password: string) => {
    loginMutation.mutate({ email, password });
  };

  const logout = () => {
    setUser(null);
    router.push("/login");
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        isLoading: isLoading || loginMutation.isPending,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
