import { useQuery } from "@tanstack/react-query";
import { IHome } from "@/types/home";
import { IApiResponse } from "@/types/response";

export function useGetHome() {
    return useQuery<IApiResponse<IHome>, Error>({
        queryKey: ["home"],
        queryFn: () =>
            fetch(`https://api.trailandtreknepal.com/home`, {
                mode: "cors",
                credentials: "include",
            })
                .then((res) => res.json()),
    });
}
