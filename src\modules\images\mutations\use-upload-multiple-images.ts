import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

interface UploadResponseData {
  url: string;
  id?: string;
  status?: string;
  name?: string;
}

interface UploadResponse {
  success: boolean;
  data: UploadResponseData[];
  message?: string;
}

export function useUploadMultipleImages() {
  const queryClient = useQueryClient();

  return useMutation<UploadResponse, Error, File[]>({
    mutationFn: (files: File[]) => {
      const formData = new FormData();
      files.forEach(file => formData.append("files", file));

      return fetch(`https://api.trailandtreknepal.com/image/uploads`, {
        method: "POST",
        body: formData,
      }).then(res => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["images-upload"] });
      toast.success("Images uploaded successfully");
    },
    onError: () => {
      toast.error("Error uploading images");
    },
  });
}
