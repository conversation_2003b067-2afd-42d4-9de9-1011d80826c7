import { useMutation, useQueryClient } from "@tanstack/react-query";
import { IApiResponse } from "@/types/response";
import { IExperience } from "@/types/home";
import { toast } from "sonner";

export function UseCreateExperience() {
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IExperience>, Error, IExperience>({
        mutationFn: (data: IExperience) =>
            fetch(`https://api.trailandtreknepal.com/home-experience`, {
                method: "POST",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['experience'] });
            toast.success("Experience Created Sucessfully");
        },
        onError: () => {
            toast.error("Error Creating Experience");
        }
    })
}