"use client";

import React, { useState, ChangeEvent } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { UseCreateHero } from '../mutations/use-create-hero';

const CreateHeroPage: React.FC = () => {
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [titles, setTitles] = useState<string[]>(['']);
  const [subtitles, setSubtitles] = useState<string[]>(['']);
  const [images, setImages] = useState<string[]>([]);
  const router = useRouter();
  
  const createHeroMutation = UseCreateHero();

  const handleVideoUrlChange = (e: ChangeEvent<HTMLInputElement>) => {
    setVideoUrl(e.target.value);
  };

  const handleTitleChange = (index: number, value: string) => {
    const newTitles = [...titles];
    newTitles[index] = value;
    setTitles(newTitles);
  };

  const handleAddTitle = () => {
    setTitles([...titles, '']);
  };

  const handleRemoveTitle = (index: number) => {
    if (titles.length > 1) {
      setTitles(titles.filter((_, i) => i !== index));
    }
  };

  const handleSubtitleChange = (index: number, value: string) => {
    const newSubtitles = [...subtitles];
    newSubtitles[index] = value;
    setSubtitles(newSubtitles);
  };

  const handleAddSubtitle = () => {
    setSubtitles([...subtitles, '']);
  };

  const handleRemoveSubtitle = (index: number) => {
    if (subtitles.length > 1) {
      setSubtitles(subtitles.filter((_, i) => i !== index));
    }
  };

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const urls = Array.from(e.target.files).map((file) => URL.createObjectURL(file));
    setImages([...images, ...urls]);
  };

  const handleRemoveImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };

  const handleCreate = async () => {
    const validTitles = titles.filter(title => title.trim());
    if (validTitles.length === 0) {
      alert('Please enter at least one title');
      return;
    }

    const heroData = {
      id: 0, 
      videoUrl: videoUrl.trim(),
      titles: titles.filter(title => title.trim()), 
      subtitles: subtitles.filter(subtitle => subtitle.trim()), 
      images: images,
    };

    try {
      await createHeroMutation.mutateAsync(heroData);
      router.push('/home/<USER>');
    } catch (error) {
      console.error('Failed to create hero:', error);
    }
  };

  const isLoading = createHeroMutation.isPending;
  const hasValidTitle = titles.some(title => title.trim());

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Add New Hero</h2>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Video URL</label>
        <input
          type="text"
          className="w-full border rounded px-3 py-2"
          value={videoUrl}
          onChange={handleVideoUrlChange}
          placeholder="Paste embed or direct video URL here"
          disabled={isLoading}
        />
        {videoUrl && (
          <div className="mt-4">
            <iframe
              className="w-full h-64 rounded"
              src={videoUrl}
              title="Preview Video"
              frameBorder="0"
              allowFullScreen
            />
          </div>
        )}
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block font-medium">Titles *</label>
          <button
            type="button"
            onClick={handleAddTitle}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            Add Title
          </button>
        </div>
        {titles.map((title, index) => (
          <div key={index} className="flex items-center gap-2 mb-2">
            <input
              type="text"
              className="flex-1 border rounded px-3 py-2"
              value={title}
              onChange={(e) => handleTitleChange(index, e.target.value)}
              placeholder={`Enter title ${index + 1}`}
              disabled={isLoading}
            />
            {titles.length > 1 && (
              <button
                type="button"
                onClick={() => handleRemoveTitle(index)}
                disabled={isLoading}
                className="px-2 py-2 text-red-600 hover:bg-red-50 rounded disabled:text-gray-400"
              >
                ✕
              </button>
            )}
          </div>
        ))}
      </div>

      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="block font-medium">Subtitles</label>
          <button
            type="button"
            onClick={handleAddSubtitle}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400"
          >
            Add Subtitle
          </button>
        </div>
        {subtitles.map((subtitle, index) => (
          <div key={index} className="flex items-center gap-2 mb-2">
            <input
              type="text"
              className="flex-1 border rounded px-3 py-2"
              value={subtitle}
              onChange={(e) => handleSubtitleChange(index, e.target.value)}
              placeholder={`Enter subtitle ${index + 1}`}
              disabled={isLoading}
            />
            {subtitles.length > 1 && (
              <button
                type="button"
                onClick={() => handleRemoveSubtitle(index)}
                disabled={isLoading}
                className="px-2 py-2 text-red-600 hover:bg-red-50 rounded disabled:text-gray-400"
              >
                ✕
              </button>
            )}
          </div>
        ))}
      </div>

      <div className="mb-6">
        <label className="block mb-1 font-medium">Images</label>
        <input
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageUpload}
          disabled={isLoading}
        />
        <div className="mt-4 grid grid-cols-3 gap-4">
          {images.map((src, idx) => (
            <div key={idx} className="relative">
              <Image
                src={src}
                alt={`Uploaded ${idx}`}
                width={150}
                height={100}
                className="w-full h-32 object-cover rounded"
              />
              <button
                onClick={() => handleRemoveImage(idx)}
                className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
                disabled={isLoading}
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6 flex space-x-2">
        <button
          onClick={handleCreate}
          disabled={isLoading || !hasValidTitle}
          className={`px-4 py-2 rounded text-white ${
            isLoading || !hasValidTitle
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-green-600 hover:bg-green-700'
          }`}
        >
          {isLoading ? 'Creating...' : 'Create'}
        </button>
        <Link href="/home/<USER>">
          <button 
            className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500"
            disabled={isLoading}
          >
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default CreateHeroPage;