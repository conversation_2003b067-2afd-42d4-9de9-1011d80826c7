import { IReview } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseDeleteReview() {
    const queryClient = useQueryClient();   
    return useMutation<IApiResponse<IReview>, Error, IReview>({
        mutationFn: (data: IReview) =>
            fetch(`https://api.trailandtreknepal.com/home-review/${data.id}`, { method: "DELETE",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['review'] });
            toast.success("Review Deleted Sucessfully");
        },
        onError: () => {
            toast.error("Error Deleting Review");
        }
    })
}