import { IApiResponse } from "@/types/response";
import User from "@/types/user";
import { useQuery } from "@tanstack/react-query";

export const useCheckLoginStatus = () => {
  return useQuery<IApiResponse<User>, Error>({
    queryKey: ["check-login-status"],
    queryFn: () =>
      fetch(`https://api.trailandtreknepal.com/auth/status`, {
        mode: "cors",
        credentials: "include",
        cache: "no-cache",
      }).then((res) => res.json()),
  });
};