"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { IReview, IReviewItem } from "@/types/home";
import { UseGetReview } from "../queries/use-get-review";
import { UseUpdateReview } from "../mutations/use-update-review";
import { toast } from "sonner";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";
import Image from "next/image";

const EditReview: React.FC = () => {
    const router = useRouter();
    const { data, isLoading, error } = UseGetReview();
    const updateReview = UseUpdateReview();

    const [reviewBlock, setReviewBlock] = useState<IReview | null>(null);

    useEffect(() => {
        if (data?.data) {
            setReviewBlock(data.data);
        }
    }, [data]);

    const handleBlockFieldChange = (field: keyof IReview, value: string) => {
        setReviewBlock((prev) => prev && { ...prev, [field]: value });
    };

    const handleReviewChange = (index: number, field: keyof IReviewItem, value: string) => {
        setReviewBlock((prev) => {
            if (!prev) return prev;
            const newReviews = [...prev.reviews];
            newReviews[index] = { ...newReviews[index], [field]: value };
            return { ...prev, reviews: newReviews };
        });
    };

    const handleAddReview = () => {
        setReviewBlock((prev) => {
            if (!prev) return prev;
            return {
                ...prev,
                reviews: [
                    ...prev.reviews,
                    {
                        id: "",
                        homeReviewId: prev.id,
                        quote: "",
                        image: "",
                        name: "",
                        destination: "",
                        date: "",
                        createdAt: "",
                        updatedAt: "",
                    },
                ],
            };
        });
    };

    const handleDeleteReview = (index: number) => {
        setReviewBlock((prev) => {
            if (!prev) return prev;
            return { ...prev, reviews: prev.reviews.filter((_, i) => i !== index) };
        });
    };

    const handleSave = () => {
        if (!reviewBlock || !reviewBlock.id) {
            toast.error("Invalid review data");
            return;
        }
        updateReview.mutate(reviewBlock, {
            onSuccess: () => {
                toast.success("Reviews updated successfully");
                router.push("/home/<USER>");
            },
            onError: (err) => {
                toast.error(`Update failed: ${err.message}`);
            },
        });
    };

    if (isLoading || !reviewBlock) return <p>Loading reviews...</p>;
    if (error) return <p>Error loading reviews: {error.message}</p>;

    return (
        <div className="container mx-auto p-6 space-y-6">
            <h1 className="text-3xl font-bold">Edit Reviews</h1>

            <div>
                <label className="block mb-1 font-medium">Heading</label>
                <input
                    type="text"
                    value={reviewBlock.title}
                    onChange={(e) => handleBlockFieldChange("title", e.target.value)}
                    className="w-full rounded border px-3 py-2"
                />
            </div>

            <div>
                <label className="block mb-1 font-medium">Subheading</label>
                <textarea
                    value={reviewBlock.subtitle}
                    onChange={(e) => handleBlockFieldChange("subtitle", e.target.value)}
                    rows={3}
                    className="w-full rounded border px-3 py-2"
                />
            </div>

            <div>
                <h2 className="text-xl font-semibold mb-4">Reviews</h2>
                {reviewBlock.reviews.map((review, index) => (
                    <div key={index} className="mb-4 border rounded p-4 bg-white space-y-3">
                        <textarea
                            placeholder="Quote"
                            value={review.quote}
                            onChange={(e) => handleReviewChange(index, "quote", e.target.value)}
                            rows={3}
                            className="w-full rounded border px-3 py-2"
                        />
                        <input
                            type="text"
                            placeholder="Name"
                            value={review.name}
                            onChange={(e) => handleReviewChange(index, "name", e.target.value)}
                            className="w-full rounded border px-3 py-2"
                        />
                        <input
                            type="text"
                            placeholder="Destination"
                            value={review.destination}
                            onChange={(e) => handleReviewChange(index, "destination", e.target.value)}
                            className="w-full rounded border px-3 py-2"
                        />
                        <input
                            type="date"
                            value={review.date}
                            onChange={(e) => handleReviewChange(index, "date", e.target.value)}
                            className="w-full rounded border px-3 py-2"
                        />
                        {review.image && !review.image.includes("example.com") ? (
                            <ImageUploadSingleWithMutation
                                label="Upload Review Image"
                                initialUrl={review.image}
                                onUploaded={(url) => handleReviewChange(index, "image", url)}
                            />
                        ) : (
                            <>
                                <Image
                                    src="/images/placeholder.jpg"
                                    alt="placeholder"
                                    width={100}
                                    height={100}
                                    className="w-40 h-24 object-cover rounded border mb-2"
                                />
                                <ImageUploadSingleWithMutation
                                    label="Upload Review Image"
                                    initialUrl=""
                                    onUploaded={(url) => handleReviewChange(index, "image", url)}
                                />
                            </>
                        )}

                        <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteReview(index)}
                            className="mt-2"
                        >
                            Delete Review
                        </Button>
                    </div>
                ))}
                <Button onClick={handleAddReview} className="mt-3">
                    + Add Review
                </Button>
            </div>

            <div className="flex justify-end space-x-3">
                <Button onClick={handleSave} className="bg-brand text-white hover:bg-brand/80">
                    Update Reviews
                </Button>
            </div>
        </div>
    );
};

export default EditReview;
