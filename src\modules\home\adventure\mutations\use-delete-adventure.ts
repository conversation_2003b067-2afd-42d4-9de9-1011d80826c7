import { IAdventure } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseDeleteAdventure() {
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IAdventure>, Error, IAdventure>({
        mutationFn: (data: IAdventure) =>
            fetch(`https://api.trailandtreknepal.com/home-adventure/${data.id}`, {
                method: "DELETE",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['adventure'] });
            toast.success("Adventure Deleted Sucessfully");
        },
        onError: () => {
            toast.error("Error Deleting Adventure");
        }
    })
}