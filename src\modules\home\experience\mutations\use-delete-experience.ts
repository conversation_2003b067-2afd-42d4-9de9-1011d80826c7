import { IExperience } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseDeleteExperience() {
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IExperience>, Error, IExperience>({
        mutationFn: (data: IExperience) =>
            fetch(`https://api.trailandtreknepal.com/home-experience/${data.id}`, {
                method: "DELETE",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['experience'] });
            toast.success("Experience Deleted Sucessfully");
        },
        onError: () => {
            toast.error("Error Deleting Experience");
        }
    })
}

