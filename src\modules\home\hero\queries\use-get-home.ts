import { IHero } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useQuery } from "@tanstack/react-query";

export function UseGetHero() {
    return useQuery<IApiResponse<IHero>, Error>({
        queryKey: ["hero"],
        queryFn: () =>
            fetch(`https://api.trailandtreknepal.com/home-hero`, {
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
            })
                .then((res) => res.json()),
    })
}