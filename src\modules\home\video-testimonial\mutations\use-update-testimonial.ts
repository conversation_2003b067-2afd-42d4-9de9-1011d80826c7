import { ITestimonial } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseUpdateTestimonial() {
    const queryClient = useQueryClient();   
    return useMutation<IApiResponse<ITestimonial>, Error, ITestimonial>({
        mutationFn: (data: ITestimonial) =>
            fetch(`https://api.trailandtreknepal.com/home-video-testimonials/${data.id}`, {
                method: "PATCH",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['testimonial'] });
            toast.success("Testimonial Updated Sucessfully");
        },
        onError: () => {
            toast.error("Error Updating Testimonial");
        }
    })
}