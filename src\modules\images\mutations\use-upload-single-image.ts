import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

interface UploadResponseData {
  url: string;
  id?: string;
  status?: string;
  name?: string;
}

interface UploadResponse {
  success: boolean;
  data: UploadResponseData;
  message?: string;
}

export function useUploadSingleImage() {
  const queryClient = useQueryClient();

  return useMutation<UploadResponse, Error, File>({
    mutationFn: (file: File) => {
      const formData = new FormData();
      formData.append("file", file);

      return fetch(`https://api.trailandtreknepal.com/image/upload`, {
        method: "POST",
        body: formData,
      }).then(res => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["image-upload"] });
      toast.success("Image uploaded successfully");
    },
    onError: () => {
      toast.error("Error uploading image");
    },
  });
}
