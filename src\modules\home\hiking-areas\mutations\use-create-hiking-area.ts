import { IHikingArea } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function UseCreateHikingArea() {
    const queryClient = useQueryClient();   
    return useMutation<IApiResponse<IHikingArea>, Error, IHikingArea>({
        mutationFn: (data: IHikingArea) =>
            fetch(`https://api.trailandtreknepal.com/home-hiking-areas`, {
                method: "POST",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['hiking-area'] });
            toast.success("Hiking Area Created Sucessfully");
        },
        onError: () => {
            toast.error("Error Creating Hiking Area");
        }
    })
}